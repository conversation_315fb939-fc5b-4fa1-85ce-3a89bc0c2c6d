// Context, TenantContext, and ObjectLookup service for metadata-driven form fields

import { apiClient } from './apiClient';
import { contextLookupCacheService } from './contextLookupCacheService';
import type {
  LookupOption,
  LookupConfig,
  ContextWithLookupsDto,
  TenantContextWithLookupsDto
} from '../types/context';

export class ContextLookupService {
  private static instance: ContextLookupService;
  private cache = new Map<string, LookupOption[]>();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes

  public static getInstance(): ContextLookupService {
    if (!ContextLookupService.instance) {
      ContextLookupService.instance = new ContextLookupService();
    }
    return ContextLookupService.instance;
  }

  /**
   * Get lookup options for a field based on objectLookupId, contextId and tenantContextId
   * Priority: ObjectLookup > TenantContext > Context (as per requirements)
   */
  async getLookupOptions(config: LookupConfig): Promise<LookupOption[]> {
    const { objectLookupId, contextId, tenantContextId, includeInactiveLookups = false } = config;

    // If no lookup IDs provided, return empty array
    if (!objectLookupId && !contextId && !tenantContextId) {
      return [];
    }

    // Create cache key
    const cacheKey = `${objectLookupId || 'none'}_${tenantContextId || 'none'}_${contextId || 'none'}_${includeInactiveLookups}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      let lookupOptions: LookupOption[] = [];

      // Priority 1: ObjectLookup (object-specific data)
      if (objectLookupId) {
        const objectOptions = await this.getObjectLookupOptions(objectLookupId);
        lookupOptions = objectOptions;
      }
      // Priority 2: TenantContext (tenant-specific data)
      else if (tenantContextId) {
        const tenantOptions = await this.getTenantContextLookups(tenantContextId, includeInactiveLookups);
        lookupOptions = tenantOptions;
      }
      // Priority 3: Context (master/common data) - only if no object lookup or tenant context
      else if (contextId) {
        const contextOptions = await this.getContextLookups(contextId, includeInactiveLookups);
        lookupOptions = contextOptions;
      }

      // Cache the results
      this.cache.set(cacheKey, lookupOptions);
      
      // Clear cache after timeout
      setTimeout(() => {
        this.cache.delete(cacheKey);
      }, this.cacheTimeout);

      return lookupOptions;
    } catch (error) {
      console.error('Error fetching lookup options:', error);
      return [];
    }
  }

  /**
   * Get Context lookups (master/common data for all tenants)
   */
  private async getContextLookups(contextId: string, includeInactiveLookups: boolean): Promise<LookupOption[]> {
    try {
      const response = await apiClient.get<ContextWithLookupsDto>(
        `/api/context/${contextId}/with-lookups?includeInactiveLookups=${includeInactiveLookups}`
      );

      // The API returns the data directly, not wrapped in a Result<T> structure for this endpoint
      if (response.data && response.data.context) {
        return this.mapContextLookupsToOptions(response.data);
      } else {
        console.warn('Context API call returned no data');
        return [];
      }
    } catch (error) {
      console.error('Error fetching context lookups:', error);
      return [];
    }
  }

  /**
   * Get TenantContext lookups (tenant-specific data)
   */
  private async getTenantContextLookups(tenantContextId: string, includeInactiveLookups: boolean): Promise<LookupOption[]> {
    try {
      const response = await apiClient.get<TenantContextWithLookupsDto>(
        `/api/context/tenant/${tenantContextId}/with-lookups?includeInactiveLookups=${includeInactiveLookups}`
      );

      // The API returns the data directly, not wrapped in a Result<T> structure for this endpoint
      if (response.data && response.data.tenantContext) {
        return this.mapTenantContextLookupsToOptions(response.data);
      } else {
        console.warn('TenantContext API call returned no data');
        return [];
      }
    } catch (error) {
      console.error('Error fetching tenant context lookups:', error);
      return [];
    }
  }

  /**
   * Get ObjectLookup options (object-specific data)
   */
  private async getObjectLookupOptions(objectLookupId: string): Promise<LookupOption[]> {
    try {
      const objectLookupData = await contextLookupCacheService.getObjectLookupData(objectLookupId);
      return this.mapObjectLookupToOptions(objectLookupData, objectLookupId);
    } catch (error) {
      console.error('Error fetching object lookup options:', error);
      return [];
    }
  }

  /**
   * Map Context lookups to unified LookupOption format
   */
  private mapContextLookupsToOptions(data: ContextWithLookupsDto): LookupOption[] {
    return data.lookups
      .filter(lookup => lookup.isActive && !lookup.isDeleted)
      .sort((a, b) => a.showSequence - b.showSequence)
      .map(lookup => ({
        id: lookup.id,
        value: lookup.value,
        label: lookup.value, // Use value as label, can be customized
        isDefault: lookup.isDefault,
        value1: lookup.value1,
        value2: lookup.value2,
        showSequence: lookup.showSequence,
        source: 'context' as const,
        contextId: lookup.contextId
      }));
  }

  /**
   * Map TenantContext lookups to unified LookupOption format
   */
  private mapTenantContextLookupsToOptions(data: TenantContextWithLookupsDto): LookupOption[] {
    return data.tenantLookups
      .filter(lookup => lookup.isActive && !lookup.isDeleted)
      .sort((a, b) => a.showSequence - b.showSequence)
      .map(lookup => ({
        id: lookup.id,
        value: lookup.value,
        label: lookup.value, // Use value as label, can be customized
        isDefault: lookup.isDefault,
        value1: lookup.value1,
        value2: lookup.value2,
        showSequence: lookup.showSequence,
        source: 'tenantContext' as const,
        tenantContextId: lookup.tenantContextId
      }));
  }

  /**
   * Map ObjectLookup data to unified LookupOption format
   */
  private mapObjectLookupToOptions(
    data: Array<{ value: string; displayText: string; showSequence: number; value1?: string; value2?: string; isDefault: boolean; refId?: string }>,
    objectLookupId: string
  ): LookupOption[] {
    return data
      .sort((a, b) => a.showSequence - b.showSequence)
      .map((lookup, index) => ({
        id: `${objectLookupId}-${lookup.refId || index}-${lookup.value || 'empty'}`,
        value: lookup.value,
        label: lookup.displayText,
        isDefault: lookup.isDefault,
        value1: lookup.value1,
        value2: lookup.value2,
        showSequence: lookup.showSequence,
        source: 'objectLookup' as const,
        objectLookupId: objectLookupId
      }));
  }

  /**
   * Clear all cached lookup data
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Clear cache for specific context
   */
  clearCacheForContext(objectLookupId?: string, contextId?: string, tenantContextId?: string): void {
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if ((objectLookupId && key.includes(objectLookupId)) ||
          (contextId && key.includes(contextId)) ||
          (tenantContextId && key.includes(tenantContextId))) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }
}

// Export singleton instance
export const contextLookupService = ContextLookupService.getInstance();
