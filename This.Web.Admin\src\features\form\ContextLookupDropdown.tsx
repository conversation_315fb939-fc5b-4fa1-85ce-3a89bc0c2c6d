// Context Lookup Dropdown component for metadata-driven form fields
// Handles ObjectLookup, Context (master) and TenantContext (tenant-specific) lookups
// Priority: ObjectLookup > TenantContext > Context (as per requirements)

import React, { useState, useEffect, useCallback } from 'react';
import { ChevronDown, Loader2, AlertCircle } from 'lucide-react';
import { contextLookupCacheService } from '../../services/contextLookupCacheService';
import type { LookupOption } from '../../types/context';

interface ContextLookupDropdownProps {
  // Core props
  id: string;
  label: string;
  value?: string;
  onChange: (value: string) => void;

  // Context configuration (from metadata)
  objectLookupId?: string; // Object lookup ID (highest priority)
  contextId?: string; // Master context ID
  tenantContextId?: string; // Tenant context ID (takes priority over contextId)
  includeInactiveLookups?: boolean;

  // Standard form props
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  placeholder?: string;
  helpText?: string;

  // Validation props
  requiredErrorMessage?: string;
  errorMessage?: string;

  // Selection props
  allowsMultiple?: boolean;
  maxSelections?: number;
  allowsCustomOptions?: boolean;
}

export const ContextLookupDropdown: React.FC<ContextLookupDropdownProps> = ({
  id,
  label,
  value,
  onChange,
  objectLookupId,
  contextId,
  tenantContextId,
  includeInactiveLookups = false,
  required = false,
  disabled = false,
  readOnly = false,
  placeholder = 'Select an option...',
  helpText,
  requiredErrorMessage,
  errorMessage,
  allowsMultiple = false,
  maxSelections: _maxSelections,
  allowsCustomOptions: _allowsCustomOptions = false
}) => {
  const [options, setOptions] = useState<LookupOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [, setForceUpdate] = useState(0);

  // Load lookup options from cache or API (instant loading for cache, async for object lookup)
  const loadOptions = useCallback(async () => {
    if (loading || (!objectLookupId && !contextId && !tenantContextId)) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let lookupOptions: LookupOption[] = [];

      // Priority: ObjectLookup > TenantContext > Context (as per requirements)
      if (objectLookupId) {
        // Handle object lookup via service
        const objectLookupData = await contextLookupCacheService.getObjectLookupData(objectLookupId);
        lookupOptions = objectLookupData.map((lookup, _index) => ({
          id: `${objectLookupId}-${lookup.value}`,
          value: lookup.value,
          label: lookup.displayText,
          showSequence: lookup.showSequence,
          isDefault: lookup.isDefault,
          source: 'objectLookup' as const,
          value1: lookup.value1,
          value2: lookup.value2,
          objectLookupId: objectLookupId
        }));
      } else if (tenantContextId) {
        // Handle tenant context lookup from cache
        const cachedLookups = contextLookupCacheService.getTenantContextLookups(tenantContextId);
        lookupOptions = cachedLookups.map((lookup, _index) => ({
          id: `${tenantContextId}-${lookup.value}`,
          value: lookup.value,
          label: lookup.displayText,
          showSequence: lookup.showSequence,
          isDefault: lookup.isDefault || false,
          source: 'tenantContext' as const,
          value1: lookup.value1,
          value2: lookup.value2,
          tenantContextId: tenantContextId
        }));
      } else if (contextId) {
        // Handle context lookup from cache
        const cachedLookups = contextLookupCacheService.getContextLookups(contextId);
        lookupOptions = cachedLookups.map((lookup, _index) => ({
          id: `${contextId}-${lookup.value}`,
          value: lookup.value,
          label: lookup.displayText,
          showSequence: lookup.showSequence,
          isDefault: lookup.isDefault || false,
          source: 'context' as const,
          value1: lookup.value1,
          value2: lookup.value2,
          contextId: contextId
        }));
      }

      setOptions(lookupOptions);
      setHasLoaded(true);

      // Check if no options found and provide appropriate error message
      if (lookupOptions.length === 0) {
        if (objectLookupId) {
          setError(`No options found for object lookup ID: ${objectLookupId}`);
        } else {
          const cacheStats = contextLookupCacheService.getCacheStats();
          console.warn('No lookup options found in cache. Cache stats:', cacheStats);

          if (cacheStats.isExpired) {
            setError('Cache expired. Please refresh the page to reload lookup data.');
          } else if (cacheStats.contextCount === 0 && cacheStats.tenantContextCount === 0 && cacheStats.objectLookupCount === 0) {
            setError('No lookup data available. Please ensure the page has loaded completely.');
          } else {
            setError(`No options found for ${tenantContextId ? 'tenant context' : 'context'} ID: ${tenantContextId || contextId}`);
          }
        }
      }
    } catch (err) {
      console.error('Error loading lookup options from cache:', err);
      setError('Failed to load options from cache');
    } finally {
      setLoading(false);
    }
  }, [objectLookupId, contextId, tenantContextId, includeInactiveLookups, loading]);

  // Load options when component mounts or when objectLookupId/contextId/tenantContextId changes
  useEffect(() => {
    if ((objectLookupId || contextId || tenantContextId) && !hasLoaded) {
      loadOptions();
    }
  }, [objectLookupId, contextId, tenantContextId, hasLoaded, loadOptions]);

  // Handle dropdown click - load options on first click (now synchronous)
  const handleDropdownClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled || readOnly) {
      return;
    }

    if (!hasLoaded) {
      loadOptions();
    }

    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);
    setForceUpdate(prev => prev + 1);
  }, [disabled, readOnly, hasLoaded, loadOptions, isOpen, options.length]);

  // Handle option selection
  const handleOptionSelect = useCallback((selectedOption: LookupOption) => {
    if (allowsMultiple) {
      // Handle multiple selection logic here if needed
      // For now, just handle single selection
      onChange(selectedOption.value);
    } else {
      onChange(selectedOption.value);
    }
    setIsOpen(false);
  }, [allowsMultiple, onChange]);

  // Get display value
  const getDisplayValue = useCallback(() => {
    if (!value) return placeholder;

    const selectedOption = options.find(opt => opt.value === value);

    if (selectedOption) {
      // Format display text as "value (value1)" same as in dropdown
      const displayText = selectedOption.value1 && selectedOption.value1.trim() !== ''
        ? `${selectedOption.value} (${selectedOption.value1})`
        : selectedOption.value;
      return displayText;
    }
    return value;
  }, [value, options, placeholder]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest(`#${id}-dropdown`)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, id]);

  // Validation
  const hasError = required && !value;
  const displayError = hasError ? (requiredErrorMessage || 'This field is required') : errorMessage;

  return (
    <div className="space-y-1">
      {/* Label */}
      <label htmlFor={id} className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {/* Dropdown */}
      <div className="relative" id={`${id}-dropdown`}>
        <button
          type="button"
          id={id}
          onClick={handleDropdownClick}
          disabled={disabled}
          className={`
            relative w-full bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm
            ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'cursor-pointer'}
            ${readOnly ? 'bg-gray-50 cursor-default' : ''}
            ${hasError ? 'border-red-300' : 'border-gray-300'}
          `}
        >
          <span className="block truncate">
            {loading ? 'Loading...' : getDisplayValue()}
          </span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            )}
          </span>
        </button>

        {/* Dropdown Options */}
        {isOpen && (
          <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
            {error ? (
              <div className="px-3 py-2 text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                {error}
              </div>
            ) : options.length === 0 ? (
              <div className="px-3 py-2 text-gray-500">
                {loading ? 'Loading options...' : 'No options available'}
              </div>
            ) : (
              options.map((option, _index) => {
                // Format display text as "value (value1)" if value1 exists, otherwise just value
                const displayText = option.value1 && option.value1.trim() !== ''
                  ? `${option.value} (${option.value1})`
                  : option.value;

                return (
                  <button
                    key={option.id}
                    type="button"
                    onClick={() => handleOptionSelect(option)}
                    className={`
                      w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none
                      ${value === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-900'}
                    `}
                  >
                    <div className="flex justify-between items-center">
                      <span>{displayText}</span>
                      {option.isDefault && (
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          Default
                        </span>
                      )}
                    </div>
                  </button>
                );
              })
            )}
          </div>
        )}
      </div>

      {/* Help Text */}
      {helpText && !displayError && (
        <p className="text-sm text-gray-500">{helpText}</p>
      )}

      {/* Error Message */}
      {displayError && (
        <p className="text-sm text-red-600">{displayError}</p>
      )}


    </div>
  );
};

export default ContextLookupDropdown;
