import { apiClient } from './apiClient';

// Types for object lookup responses
export interface ObjectLookupDataResponse {
  objectLookup: {
    id: string;
    name: string;
    sourceType: string;
    displayField: string;
    valueField: string;
    isActive: boolean;
  };
  objectLookUpValues: {
    values: Array<{
      value: any;
      refId: string;
    }>;
    totalValues: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    message: string;
  };
  message: string;
}

export interface ObjectLookupCacheData {
  [objectLookupId: string]: Array<{
    value: string;
    displayText: string;
    showSequence: number;
    value1?: string;
    value2?: string;
    isDefault: boolean;
    refId?: string;
  }>;
}

// Types for bulk context responses
export interface ContextWithLookupsDto {
  context: {
    id: string;
    name: string;
    description?: string;
    isActive: boolean;
    createdAt: string;
    modifiedAt: string;
    lookups: {
      id: string;
      contextId: string;
      value: string;
      displayText: string;
      showSequence: number;
      isActive: boolean;
      createdAt: string;
      modifiedAt: string;
      value1: string;
      value2: string;
      isDefault: boolean;
    }[];
  };
  lookups: Array<{
    id: string;
    contextId: string;
    value: string;
    displayText: string;
    showSequence: number;
    isActive: boolean;
    createdAt: string;
    modifiedAt: string;
    value1: string;
    value2: string;
    isDefault: boolean;
  }>;
}

export interface TenantContextWithLookupsDto {
  tenantContext: {
    id: string;
    name: string;
    description?: string;
    isActive: boolean;
    createdAt: string;
    modifiedAt: string;
  };
  tenantLookups: Array<{
    id: string;
    tenantContextId: string;
    value: string;
    displayText: string;
    showSequence: number;
    isActive: boolean;
    createdAt: string;
    modifiedAt: string;
    value1: string;
    value2: string;
    isDefault: boolean;
  }>;
}

export interface BulkContextWithLookupsDto {
  contextsWithLookups: ContextWithLookupsDto[];
  totalContextsCount: number;
  totalLookupsCount: number;
  notFoundContextIds: string[];
  deletedContextIds: string[];
}

export interface BulkTenantContextWithLookupsDto {
  tenantContextsWithLookups: TenantContextWithLookupsDto[];
  totalTenantContextsCount: number;
  totalTenantLookupsCount: number;
  notFoundTenantContextIds: string[];
  deletedTenantContextIds: string[];
}

// Local storage keys
const CONTEXT_CACHE_KEY = 'context-lookups-cache';
const TENANT_CONTEXT_CACHE_KEY = 'tenant-context-lookups-cache';
const OBJECT_LOOKUP_CACHE_KEY = 'object-lookup-cache';
const CACHE_TIMESTAMP_KEY = 'context-cache-timestamp';
const CACHE_EXPIRY_HOURS = 24; // Cache expires after 24 hours

export class ContextLookupCacheService {
  private static instance: ContextLookupCacheService;

  static getInstance(): ContextLookupCacheService {
    if (!ContextLookupCacheService.instance) {
      ContextLookupCacheService.instance = new ContextLookupCacheService();
    }
    return ContextLookupCacheService.instance;
  }

  /**
   * Extract unique context, tenant context, and object lookup IDs from metadata
   */
  extractContextIds(metadata: any[]): { contextIds: string[]; tenantContextIds: string[]; objectLookupIds: string[] } {
    const contextIds = new Set<string>();
    const tenantContextIds = new Set<string>();
    const objectLookupIds = new Set<string>();

    metadata.forEach(item => {
      // Handle both direct metadata and nested metadata structures
      const meta = item.metadata || item;

      if (meta.contextId && typeof meta.contextId === 'string') {
        contextIds.add(meta.contextId);
      }

      if (meta.tenantContextId && typeof meta.tenantContextId === 'string') {
        tenantContextIds.add(meta.tenantContextId);
      }

      if (meta.objectLookupId && typeof meta.objectLookupId === 'string') {
        objectLookupIds.add(meta.objectLookupId);
      }
    });

    return {
      contextIds: Array.from(contextIds),
      tenantContextIds: Array.from(tenantContextIds),
      objectLookupIds: Array.from(objectLookupIds)
    };
  }

  /**
   * Fetch bulk contexts with lookups and cache them
   */
  async fetchAndCacheBulkContexts(contextIds: string[]): Promise<BulkContextWithLookupsDto | null> {
    if (!contextIds.length) {
      return null;
    }

    try {
      // Add query parameter to URL
      const url = '/api/context/bulk/with-lookups?includeInactiveLookups=false';
      const response = await apiClient.post<BulkContextWithLookupsDto>(url, contextIds);

      if (response.data) {
        // Cache the response
        this.cacheContextData(response.data);

        return response.data;
      }

      return null;
    } catch (error) {
      console.error('Error fetching bulk contexts with lookups:', error);
      return null;
    }
  }

  /**
   * Fetch bulk tenant contexts with lookups and cache them
   */
  async fetchAndCacheBulkTenantContexts(tenantContextIds: string[]): Promise<BulkTenantContextWithLookupsDto | null> {
    if (!tenantContextIds.length) {
      return null;
    }

    try {
      // Add query parameter to URL
      const url = '/api/context/tenant/bulk/with-lookups?includeInactiveLookups=false';
      const response = await apiClient.post<BulkTenantContextWithLookupsDto>(url, tenantContextIds);

      if (response.data) {
        // Cache the response
        this.cacheTenantContextData(response.data);

        return response.data;
      }

      return null;
    } catch (error) {
      console.error('Error fetching bulk tenant contexts with lookups:', error);
      return null;
    }
  }

  /**
   * Cache context data in localStorage
   */
  private cacheContextData(data: BulkContextWithLookupsDto): void {
    try {
      const existingCache = this.getContextCache();
      
      // Merge with existing cache
      const mergedContexts = new Map<string, ContextWithLookupsDto>();
      
      // Add existing cached contexts
      existingCache.contextsWithLookups.forEach(ctx => {
        mergedContexts.set(ctx.context.id, ctx);
      });
      
      // Add new contexts (overwrite if exists)
      data.contextsWithLookups.forEach(ctx => {
        mergedContexts.set(ctx.context.id, ctx);
      });

      const mergedData: BulkContextWithLookupsDto = {
        contextsWithLookups: Array.from(mergedContexts.values()),
        totalContextsCount: mergedContexts.size,
        totalLookupsCount: Array.from(mergedContexts.values()).reduce((sum, ctx) => sum + ctx.lookups.length, 0),
        notFoundContextIds: data.notFoundContextIds,
        deletedContextIds: data.deletedContextIds
      };

      localStorage.setItem(CONTEXT_CACHE_KEY, JSON.stringify(mergedData));
      localStorage.setItem(CACHE_TIMESTAMP_KEY, new Date().toISOString());
    } catch (error) {
      console.error('Error caching context data:', error);
    }
  }

  /**
   * Cache tenant context data in localStorage
   */
  private cacheTenantContextData(data: BulkTenantContextWithLookupsDto): void {
    try {
      const existingCache = this.getTenantContextCache();
      
      // Merge with existing cache
      const mergedTenantContexts = new Map<string, TenantContextWithLookupsDto>();
      
      // Add existing cached tenant contexts
      existingCache.tenantContextsWithLookups.forEach(tc => {
        mergedTenantContexts.set(tc.tenantContext.id, tc);
      });
      
      // Add new tenant contexts (overwrite if exists)
      data.tenantContextsWithLookups.forEach(tc => {
        mergedTenantContexts.set(tc.tenantContext.id, tc);
      });

      const mergedData: BulkTenantContextWithLookupsDto = {
        tenantContextsWithLookups: Array.from(mergedTenantContexts.values()),
        totalTenantContextsCount: mergedTenantContexts.size,
        totalTenantLookupsCount: Array.from(mergedTenantContexts.values()).reduce((sum, tc) => sum + tc.tenantLookups.length, 0),
        notFoundTenantContextIds: data.notFoundTenantContextIds,
        deletedTenantContextIds: data.deletedTenantContextIds
      };

      localStorage.setItem(TENANT_CONTEXT_CACHE_KEY, JSON.stringify(mergedData));
      localStorage.setItem(CACHE_TIMESTAMP_KEY, new Date().toISOString());
    } catch (error) {
      console.error('Error caching tenant context data:', error);
    }
  }

  /**
   * Get cached context data
   */
  getContextCache(): BulkContextWithLookupsDto {
    try {
      const cached = localStorage.getItem(CONTEXT_CACHE_KEY);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.error('Error reading context cache:', error);
    }
    
    return {
      contextsWithLookups: [],
      totalContextsCount: 0,
      totalLookupsCount: 0,
      notFoundContextIds: [],
      deletedContextIds: []
    };
  }

  /**
   * Get cached tenant context data
   */
  getTenantContextCache(): BulkTenantContextWithLookupsDto {
    try {
      const cached = localStorage.getItem(TENANT_CONTEXT_CACHE_KEY);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.error('Error reading tenant context cache:', error);
    }
    
    return {
      tenantContextsWithLookups: [],
      totalTenantContextsCount: 0,
      totalTenantLookupsCount: 0,
      notFoundTenantContextIds: [],
      deletedTenantContextIds: []
    };
  }

  /**
   * Get context lookups by context ID from cache
   */
  getContextLookups(contextId: string): Array<{ value: string; displayText: string; showSequence: number; value1: string; value2: string; isDefault: boolean }> {
    const cache = this.getContextCache();
    const context = cache.contextsWithLookups.find(ctx => ctx.context.id === contextId);
    return context ? context.lookups.filter(lookup => lookup.isActive).map(lookup => ({
      value: lookup.value,
      displayText: lookup.displayText,
      showSequence: lookup.showSequence,
      value1: lookup.value1,
      value2: lookup.value2,
      isDefault: lookup.isDefault
    })).sort((a, b) => a.showSequence - b.showSequence) : [];
  }

  /**
   * Get tenant context lookups by tenant context ID from cache
   */
  getTenantContextLookups(tenantContextId: string): Array<{ value: string; displayText: string; showSequence: number; value1: string; value2: string; isDefault: boolean }> {
    const cache = this.getTenantContextCache();
    const tenantContext = cache.tenantContextsWithLookups.find(tc => tc.tenantContext.id === tenantContextId);
    return tenantContext ? tenantContext.tenantLookups.filter(lookup => lookup.isActive).map(lookup => ({
      value: lookup.value,
      displayText: lookup.displayText,
      showSequence: lookup.showSequence,
      value1: lookup.value1,
      value2: lookup.value2,
      isDefault: lookup.isDefault
    })).sort((a, b) => a.showSequence - b.showSequence) : [];
  }

  /**
   * Get object lookup data by object lookup ID (with caching)
   */
  async getObjectLookupData(objectLookupId: string): Promise<Array<{ value: string; displayText: string; showSequence: number; value1?: string; value2?: string; isDefault: boolean; refId?: string }>> {
    console.log('🔍 [ObjectLookup] getObjectLookupData called with objectLookupId:', objectLookupId);
    console.log('🔍 [ObjectLookup] objectLookupId type:', typeof objectLookupId);
    console.log('🔍 [ObjectLookup] objectLookupId truthy:', !!objectLookupId);

    try {
      // Check cache first
      console.log('🔍 [ObjectLookup] Checking cache for objectLookupId:', objectLookupId);
      const cachedData = this.getObjectLookupFromCache(objectLookupId);
      console.log('🔍 [ObjectLookup] Cached data found:', cachedData.length > 0 ? `${cachedData.length} items` : 'none');

      if (cachedData.length > 0) {
        console.log('🔍 [ObjectLookup] Returning cached data:', cachedData);
        return cachedData;
      }

      // Fetch from API if not in cache
      console.log('🔍 [ObjectLookup] Cache miss - making API call to:', `/api/objectvalues/object-lookup-data/${objectLookupId}`);
      const response = await apiClient.get<ObjectLookupDataResponse>(`/api/objectvalues/object-lookup-data/${objectLookupId}`);
      console.log('🔍 [ObjectLookup] API response received:', response);
      console.log('🔍 [ObjectLookup] API response status:', response.status);
      console.log('🔍 [ObjectLookup] API response data:', response.data);

      if (response.data && response.data.objectLookUpValues.values) {
        console.log('🔍 [ObjectLookup] Raw API values:', response.data.objectLookUpValues.values);

        // Transform API response to match expected format
        const transformedData = response.data.objectLookUpValues.values.map((item, index) => ({
          value: item.value?.toString() || '',
          displayText: item.value?.toString() || '', // Use value as display text since API doesn't provide separate display text
          showSequence: index, // Use array index for consistent ordering
          value1: undefined, // Not available in object lookup response
          value2: undefined, // Not available in object lookup response
          isDefault: false, // API doesn't provide default indicators
          refId: item.refId // Include refId for unique identification
        }));

        console.log('🔍 [ObjectLookup] Transformed data:', transformedData);

        // Cache the transformed data
        this.cacheObjectLookupData(objectLookupId, transformedData);
        console.log('🔍 [ObjectLookup] Data cached successfully');

        return transformedData;
      } else {
        console.warn('🔍 [ObjectLookup] API response missing expected data structure');
        console.warn('🔍 [ObjectLookup] Response data:', response.data);
      }

      console.log('🔍 [ObjectLookup] Returning empty array');
      return [];
    } catch (error) {
      console.error('🔍 [ObjectLookup] Error fetching object lookup data:', error);
      console.error('🔍 [ObjectLookup] Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        objectLookupId
      });
      return [];
    }
  }

  /**
   * Get object lookup data from cache
   */
  private getObjectLookupFromCache(objectLookupId: string): Array<{ value: string; displayText: string; showSequence: number; value1?: string; value2?: string; isDefault: boolean; refId?: string }> {
    try {
      const cached = localStorage.getItem(OBJECT_LOOKUP_CACHE_KEY);
      if (cached) {
        const cacheData: ObjectLookupCacheData = JSON.parse(cached);
        return cacheData[objectLookupId] || [];
      }
    } catch (error) {
      console.error('Error reading object lookup cache:', error);
    }
    return [];
  }

  /**
   * Cache object lookup data
   */
  private cacheObjectLookupData(objectLookupId: string, data: Array<{ value: string; displayText: string; showSequence: number; value1?: string; value2?: string; isDefault: boolean; refId?: string }>): void {
    try {
      const existingCache = this.getObjectLookupCache();
      existingCache[objectLookupId] = data;

      localStorage.setItem(OBJECT_LOOKUP_CACHE_KEY, JSON.stringify(existingCache));
      localStorage.setItem(CACHE_TIMESTAMP_KEY, new Date().toISOString());
    } catch (error) {
      console.error('Error caching object lookup data:', error);
    }
  }

  /**
   * Get all cached object lookup data
   */
  private getObjectLookupCache(): ObjectLookupCacheData {
    try {
      const cached = localStorage.getItem(OBJECT_LOOKUP_CACHE_KEY);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      console.error('Error reading object lookup cache:', error);
    }
    return {};
  }

  /**
   * Check if cache is expired
   */
  isCacheExpired(): boolean {
    try {
      const timestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY);
      if (!timestamp) return true;
      
      const cacheTime = new Date(timestamp);
      const now = new Date();
      const diffHours = (now.getTime() - cacheTime.getTime()) / (1000 * 60 * 60);
      
      return diffHours > CACHE_EXPIRY_HOURS;
    } catch (error) {
      return true;
    }
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    localStorage.removeItem(CONTEXT_CACHE_KEY);
    localStorage.removeItem(TENANT_CONTEXT_CACHE_KEY);
    localStorage.removeItem(OBJECT_LOOKUP_CACHE_KEY);
    localStorage.removeItem(CACHE_TIMESTAMP_KEY);
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    contextCount: number;
    tenantContextCount: number;
    objectLookupCount: number;
    totalLookups: number;
    cacheAge: string;
    isExpired: boolean;
  } {
    const contextCache = this.getContextCache();
    const tenantContextCache = this.getTenantContextCache();
    const objectLookupCache = this.getObjectLookupCache();
    const timestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY);

    // Calculate object lookup statistics
    const objectLookupCount = Object.keys(objectLookupCache).length;
    const objectLookupTotalValues = Object.values(objectLookupCache).reduce((sum, lookups) => sum + lookups.length, 0);

    let cacheAge = 'Unknown';
    if (timestamp) {
      const cacheTime = new Date(timestamp);
      const now = new Date();
      const diffMinutes = Math.floor((now.getTime() - cacheTime.getTime()) / (1000 * 60));
      cacheAge = diffMinutes < 60 ? `${diffMinutes} minutes` : `${Math.floor(diffMinutes / 60)} hours`;
    }

    return {
      contextCount: contextCache.totalContextsCount,
      tenantContextCount: tenantContextCache.totalTenantContextsCount,
      objectLookupCount: objectLookupCount,
      totalLookups: contextCache.totalLookupsCount + tenantContextCache.totalTenantLookupsCount + objectLookupTotalValues,
      cacheAge,
      isExpired: this.isCacheExpired()
    };
  }
}

export const contextLookupCacheService = ContextLookupCacheService.getInstance();
