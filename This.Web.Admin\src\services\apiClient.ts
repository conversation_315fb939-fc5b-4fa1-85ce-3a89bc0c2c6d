// Centralized API client for all HTTP requests
// Handles authentication, tenant headers, and common request/response logic

interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

interface ApiError {
  message: string;
  status?: number;
  statusText?: string;
  data?: any;
}

class ApiClient {
  private static instance: ApiClient;
  private readonly baseURL: string;
  private readonly defaultHeaders: Record<string, string>;
  private readonly timeout: number;

  constructor() {
    this.baseURL = 'https://this-v3-h2ggexbrfkc7dmf2.centralindia-01.azurewebsites.net';
    this.defaultHeaders = {
      'accept': 'application/json',
      'Content-Type': 'application/json',
      'tenant': 'kitchsync' // Default tenant - should be configurable
    };
    this.timeout = 30000; // 30 seconds
  }

  public static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  /**
   * Set tenant header for multi-tenant requests
   */
  setTenant(tenantId: string): void {
    this.defaultHeaders.tenant = tenantId;
  }

  /**
   * Get current tenant
   */
  getTenant(): string {
    return this.defaultHeaders.tenant;
  }

  /**
   * Make a GET request
   */
  async get<T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    console.log('🔍 [ApiClient] GET request initiated:', url);
    console.log('🔍 [ApiClient] Additional headers:', headers);
    return this.request<T>('GET', url, undefined, headers);
  }

  /**
   * Make a POST request
   */
  async post<T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('POST', url, data, headers);
  }

  /**
   * Make a PUT request
   */
  async put<T = any>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', url, data, headers);
  }

  /**
   * Make a DELETE request
   */
  async delete<T = any>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', url, undefined, headers);
  }

  /**
   * Core request method
   */
  private async request<T = any>(
    method: string,
    url: string,
    data?: any,
    additionalHeaders?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    try {
      // Construct full URL
      const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

      // Merge headers
      const headers = {
        ...this.defaultHeaders,
        ...additionalHeaders
      };

      console.log('🔍 [ApiClient] Making request:', {
        method,
        fullUrl,
        headers: this.sanitizeHeaders(headers),
        data: data ? 'Present' : 'None'
      });

      // Prepare request options
      const requestOptions: RequestInit = {
        method,
        headers,
        signal: AbortSignal.timeout(this.timeout)
      };

      // Add body for POST/PUT requests
      if (data && (method === 'POST' || method === 'PUT')) {
        requestOptions.body = JSON.stringify(data);
      }

      // Make the request
      console.log('🔍 [ApiClient] Sending fetch request to:', fullUrl);
      const response = await fetch(fullUrl, requestOptions);
      console.log('🔍 [ApiClient] Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      });

      // Parse response
      let responseData: T;
      const contentType = response.headers.get('content-type');
      console.log('🔍 [ApiClient] Response content-type:', contentType);

      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
        console.log('🔍 [ApiClient] Parsed JSON response:', responseData);
      } else {
        responseData = await response.text() as any;
        console.log('🔍 [ApiClient] Parsed text response:', responseData);
      }

      // Check if request was successful
      if (!response.ok) {
        console.error('🔍 [ApiClient] Request failed:', {
          status: response.status,
          statusText: response.statusText,
          data: responseData
        });
        const error: ApiError = {
          message: `API request failed: ${response.status} ${response.statusText}`,
          status: response.status,
          statusText: response.statusText,
          data: responseData
        };
        throw error;
      }

      console.log('🔍 [ApiClient] Request successful, returning response');
      return {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      };

    } catch (error) {
      console.error(`API ${method} ${url} failed:`, error);

      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`API request failed: ${error}`);
      }
    }
  }

  /**
   * Sanitize headers for logging (remove sensitive information)
   */
  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized = { ...headers };

    // List of headers to sanitize
    const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie'];

    sensitiveHeaders.forEach(header => {
      if (sanitized[header]) {
        sanitized[header] = '***';
      }
    });

    return sanitized;
  }

  /**
   * Set authorization header
   */
  setAuthToken(token: string): void {
    this.defaultHeaders.authorization = `Bearer ${token}`;
  }

  /**
   * Remove authorization header
   */
  clearAuthToken(): void {
    delete this.defaultHeaders.authorization;
  }

  /**
   * Get base URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }

  /**
   * Update default headers
   */
  updateDefaultHeaders(headers: Record<string, string>): void {
    Object.assign(this.defaultHeaders, headers);
  }

  /**
   * Get current default headers
   */
  getDefaultHeaders(): Record<string, string> {
    return { ...this.defaultHeaders };
  }
}

// Export singleton instance
export const apiClient = ApiClient.getInstance();
export type { ApiResponse, ApiError };
